# Manual Koneksi WebSocket - ATMA Notification Service

## Overview

ATMA Notification Service menyediakan real-time notifications melalui WebSocket menggunakan Socket.IO. Service ini mengirimkan notifikasi untuk status analisis (started, completed, failed) kepada user yang sedang terhubung.

## Server Information

- **URL**: `http://localhost:3005` (development)
- **Protocol**: Socket.IO v4.7.2
- **Authentication**: JWT Token required
- **CORS**: Enabled untuk semua origins

## Quick Start

### 1. Install Socket.IO Client

```bash
npm install socket.io-client
```

### 2. Basic Connection (JavaScript/React)

```javascript
import { io } from 'socket.io-client';

// Inisialisasi koneksi
const socket = io('http://localhost:3005', {
  autoConnect: false, // Manual connection
  transports: ['websocket', 'polling']
});

// Connect dengan token
function connectWithAuth(token) {
  socket.connect();
  
  socket.on('connect', () => {
    console.log('Connected to notification service');
    // Kirim token untuk autentikasi
    socket.emit('authenticate', { token });
  });
}

// Handle authentication response
socket.on('authenticated', (data) => {
  console.log('Authentication successful:', data);
  // { success: true, userId: "uuid", email: "<EMAIL>" }
});

socket.on('auth_error', (error) => {
  console.error('Authentication failed:', error);
  // { message: "Token required" } atau { message: "Authentication timeout" }
});
```

## Authentication Flow

### 1. Koneksi dan Autentikasi

```javascript
// Step 1: Connect
socket.connect();

// Step 2: Authenticate (harus dilakukan dalam 10 detik)
socket.on('connect', () => {
  const token = localStorage.getItem('authToken'); // atau dari state management
  socket.emit('authenticate', { token });
});

// Step 3: Handle auth response
socket.on('authenticated', (data) => {
  console.log('User authenticated:', data.userId);
  // Sekarang bisa menerima notifikasi
});

socket.on('auth_error', (error) => {
  console.error('Auth error:', error.message);
  socket.disconnect();
});
```

### 2. JWT Token Format

Token harus berisi payload:
```json
{
  "id": "user-uuid",
  "email": "<EMAIL>",
  "iat": 1234567890,
  "exp": 1234567890
}
```

## Event Listeners

### 1. Analysis Events

```javascript
// Analisis dimulai
socket.on('analysis-started', (data) => {
  console.log('Analysis started:', data);
  /*
  {
    jobId: "uuid",
    status: "started",
    message: "Your analysis has started processing...",
    metadata: {
      assessmentName: "Assessment Name",
      estimatedProcessingTime: "5-10 minutes"
    },
    timestamp: "2024-01-01T12:00:00.000Z"
  }
  */
});

// Analisis selesai
socket.on('analysis-complete', (data) => {
  console.log('Analysis completed:', data);
  /*
  {
    jobId: "uuid",
    resultId: "uuid",
    status: "completed",
    message: "Your analysis is ready!",
    metadata: {
      assessmentName: "Assessment Name",
      processingTime: "7 minutes"
    },
    timestamp: "2024-01-01T12:00:00.000Z"
  }
  */
});

// Analisis gagal
socket.on('analysis-failed', (data) => {
  console.log('Analysis failed:', data);
  /*
  {
    jobId: "uuid",
    error: "Error message",
    message: "Analysis failed. Please try again.",
    metadata: {
      assessmentName: "Assessment Name",
      errorType: "PROCESSING_ERROR"
    },
    timestamp: "2024-01-01T12:00:00.000Z"
  }
  */
});
```

### 2. Connection Events

```javascript
// Koneksi berhasil
socket.on('connect', () => {
  console.log('Connected to server');
});

// Koneksi terputus
socket.on('disconnect', (reason) => {
  console.log('Disconnected:', reason);
  // Auto-reconnect akan dilakukan otomatis
});

// Reconnecting
socket.on('reconnecting', (attemptNumber) => {
  console.log('Reconnecting attempt:', attemptNumber);
});

// Reconnect berhasil
socket.on('reconnect', (attemptNumber) => {
  console.log('Reconnected after', attemptNumber, 'attempts');
  // Perlu authenticate ulang
  const token = localStorage.getItem('authToken');
  socket.emit('authenticate', { token });
});
```

## Complete React Hook Example

```javascript
import { useEffect, useRef, useState } from 'react';
import { io } from 'socket.io-client';

export const useNotificationSocket = (token) => {
  const socketRef = useRef(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    if (!token) return;

    // Initialize socket
    socketRef.current = io('http://localhost:3005', {
      autoConnect: false,
      transports: ['websocket', 'polling']
    });

    const socket = socketRef.current;

    // Connection events
    socket.on('connect', () => {
      setIsConnected(true);
      socket.emit('authenticate', { token });
    });

    socket.on('disconnect', () => {
      setIsConnected(false);
      setIsAuthenticated(false);
    });

    socket.on('reconnect', () => {
      socket.emit('authenticate', { token });
    });

    // Authentication events
    socket.on('authenticated', (data) => {
      setIsAuthenticated(true);
      console.log('Authenticated as:', data.email);
    });

    socket.on('auth_error', (error) => {
      setIsAuthenticated(false);
      console.error('Auth error:', error.message);
    });

    // Notification events
    socket.on('analysis-started', (data) => {
      setNotifications(prev => [...prev, { type: 'started', ...data }]);
    });

    socket.on('analysis-complete', (data) => {
      setNotifications(prev => [...prev, { type: 'completed', ...data }]);
    });

    socket.on('analysis-failed', (data) => {
      setNotifications(prev => [...prev, { type: 'failed', ...data }]);
    });

    // Connect
    socket.connect();

    // Cleanup
    return () => {
      socket.disconnect();
    };
  }, [token]);

  const clearNotifications = () => {
    setNotifications([]);
  };

  return {
    isConnected,
    isAuthenticated,
    notifications,
    clearNotifications
  };
};
```

## Usage in React Component

```javascript
import React from 'react';
import { useNotificationSocket } from './hooks/useNotificationSocket';

const Dashboard = () => {
  const token = localStorage.getItem('authToken');
  const { isConnected, isAuthenticated, notifications } = useNotificationSocket(token);

  return (
    <div>
      <div className="connection-status">
        Status: {isConnected ? 'Connected' : 'Disconnected'}
        {isConnected && (isAuthenticated ? ' (Authenticated)' : ' (Not Authenticated)')}
      </div>

      <div className="notifications">
        {notifications.map((notification, index) => (
          <div key={index} className={`notification ${notification.type}`}>
            <h4>{notification.message}</h4>
            <p>Job ID: {notification.jobId}</p>
            <small>{new Date(notification.timestamp).toLocaleString()}</small>
          </div>
        ))}
      </div>
    </div>
  );
};
```

## Error Handling

### Common Errors

1. **Authentication Timeout**
   ```javascript
   socket.on('auth_error', (error) => {
     if (error.message === 'Authentication timeout') {
       // Reconnect dan authenticate ulang
       socket.disconnect();
       socket.connect();
     }
   });
   ```

2. **Invalid Token**
   ```javascript
   socket.on('auth_error', (error) => {
     if (error.message === 'Token required' || error.message.includes('invalid')) {
       // Redirect ke login atau refresh token
       window.location.href = '/login';
     }
   });
   ```

3. **Connection Lost**
   ```javascript
   socket.on('disconnect', (reason) => {
     if (reason === 'io server disconnect') {
       // Server memutuskan koneksi, reconnect manual
       socket.connect();
     }
     // Untuk reason lain, auto-reconnect akan berjalan otomatis
   });
   ```

## Configuration Options

```javascript
const socket = io('http://localhost:3005', {
  // Auto-connect saat inisialisasi
  autoConnect: true,
  
  // Transport methods
  transports: ['websocket', 'polling'],
  
  // Reconnection settings
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  reconnectionDelayMax: 5000,
  
  // Timeout settings
  timeout: 20000,
  
  // Force new connection
  forceNew: false
});
```

## Testing Connection

### Manual Test

```javascript
// Test connection
const testSocket = io('http://localhost:3005');

testSocket.on('connect', () => {
  console.log('Test connection successful');
  
  // Test authentication dengan token dummy
  testSocket.emit('authenticate', { 
    token: 'your-jwt-token-here' 
  });
});

testSocket.on('authenticated', (data) => {
  console.log('Test authentication successful:', data);
  testSocket.disconnect();
});

testSocket.on('auth_error', (error) => {
  console.log('Test authentication failed:', error);
  testSocket.disconnect();
});
```

## Production Considerations

1. **Environment Variables**
   ```javascript
   const SOCKET_URL = process.env.REACT_APP_NOTIFICATION_SERVICE_URL || 'http://localhost:3005';
   ```

2. **Token Refresh**
   ```javascript
   // Handle token refresh
   socket.on('auth_error', async (error) => {
     if (error.message.includes('expired')) {
       const newToken = await refreshAuthToken();
       socket.emit('authenticate', { token: newToken });
     }
   });
   ```

3. **Graceful Shutdown**
   ```javascript
   // Cleanup saat component unmount atau user logout
   useEffect(() => {
     return () => {
       if (socketRef.current) {
         socketRef.current.disconnect();
       }
     };
   }, []);
   ```

## Troubleshooting

### Common Issues

1. **CORS Error**: Pastikan frontend URL sudah dikonfigurasi di server
2. **Authentication Timeout**: Pastikan emit authenticate dalam 10 detik setelah connect
3. **Token Invalid**: Pastikan JWT token valid dan belum expired
4. **Connection Failed**: Periksa apakah notification service berjalan di port 3005

### Debug Mode

```javascript
// Enable debug mode
localStorage.debug = 'socket.io-client:socket';

// Atau set saat inisialisasi
const socket = io('http://localhost:3005', {
  debug: true
});
```

## Framework-Specific Examples

### Vue.js Implementation

```javascript
// composables/useNotificationSocket.js
import { ref, onMounted, onUnmounted } from 'vue';
import { io } from 'socket.io-client';

export function useNotificationSocket(token) {
  const socket = ref(null);
  const isConnected = ref(false);
  const isAuthenticated = ref(false);
  const notifications = ref([]);

  onMounted(() => {
    if (!token.value) return;

    socket.value = io('http://localhost:3005', {
      autoConnect: false,
      transports: ['websocket', 'polling']
    });

    socket.value.on('connect', () => {
      isConnected.value = true;
      socket.value.emit('authenticate', { token: token.value });
    });

    socket.value.on('authenticated', (data) => {
      isAuthenticated.value = true;
    });

    socket.value.on('analysis-complete', (data) => {
      notifications.value.push({ type: 'completed', ...data });
    });

    socket.value.connect();
  });

  onUnmounted(() => {
    if (socket.value) {
      socket.value.disconnect();
    }
  });

  return {
    isConnected,
    isAuthenticated,
    notifications
  };
}
```

### Angular Service

```typescript
// notification-socket.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { io, Socket } from 'socket.io-client';

@Injectable({
  providedIn: 'root'
})
export class NotificationSocketService {
  private socket: Socket;
  private isConnectedSubject = new BehaviorSubject<boolean>(false);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  private notificationsSubject = new BehaviorSubject<any[]>([]);

  public isConnected$ = this.isConnectedSubject.asObservable();
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  public notifications$ = this.notificationsSubject.asObservable();

  connect(token: string): void {
    this.socket = io('http://localhost:3005', {
      autoConnect: false,
      transports: ['websocket', 'polling']
    });

    this.socket.on('connect', () => {
      this.isConnectedSubject.next(true);
      this.socket.emit('authenticate', { token });
    });

    this.socket.on('authenticated', () => {
      this.isAuthenticatedSubject.next(true);
    });

    this.socket.on('analysis-complete', (data) => {
      const current = this.notificationsSubject.value;
      this.notificationsSubject.next([...current, { type: 'completed', ...data }]);
    });

    this.socket.connect();
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.isConnectedSubject.next(false);
      this.isAuthenticatedSubject.next(false);
    }
  }
}
```

### Vanilla JavaScript

```html
<!DOCTYPE html>
<html>
<head>
    <title>ATMA Notifications</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
</head>
<body>
    <div id="status">Disconnected</div>
    <div id="notifications"></div>

    <script>
        const socket = io('http://localhost:3005', {
            autoConnect: false
        });

        const statusEl = document.getElementById('status');
        const notificationsEl = document.getElementById('notifications');

        socket.on('connect', () => {
            statusEl.textContent = 'Connected';
            const token = localStorage.getItem('authToken');
            socket.emit('authenticate', { token });
        });

        socket.on('authenticated', () => {
            statusEl.textContent = 'Connected & Authenticated';
        });

        socket.on('analysis-complete', (data) => {
            const notificationEl = document.createElement('div');
            notificationEl.innerHTML = `
                <h4>${data.message}</h4>
                <p>Job: ${data.jobId}</p>
                <small>${new Date(data.timestamp).toLocaleString()}</small>
            `;
            notificationsEl.appendChild(notificationEl);
        });

        // Connect
        socket.connect();
    </script>
</body>
</html>
```

## API Endpoints (HTTP Fallback)

Selain WebSocket, service juga menyediakan HTTP endpoints untuk debugging:

### Health Check
```
GET http://localhost:3005/health
```

Response:
```json
{
  "success": true,
  "service": "notification-service",
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "connections": {
    "total": 5,
    "authenticated": 3,
    "users": 3
  }
}
```

### Connection Debug
```
GET http://localhost:3005/debug/connections
Headers: {
  "x-service-key": "internal_service_secret_key"
}
```

## Environment Configuration

### Development (.env)
```env
PORT=3005
NODE_ENV=development
JWT_SECRET=your_super_secret_jwt_key_here
CORS_ORIGIN=*
SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000
```

### Production (.env)
```env
PORT=3005
NODE_ENV=production
JWT_SECRET=your_production_jwt_secret
CORS_ORIGIN=https://yourdomain.com,https://app.yourdomain.com
SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000
```

## Security Best Practices

1. **Token Validation**: Selalu validasi JWT token di server
2. **CORS Configuration**: Set specific origins untuk production
3. **Rate Limiting**: Implementasikan rate limiting untuk prevent abuse
4. **SSL/TLS**: Gunakan HTTPS/WSS untuk production
5. **Token Expiry**: Handle token expiry dengan graceful refresh

## Performance Optimization

1. **Connection Pooling**: Gunakan satu koneksi per user session
2. **Event Debouncing**: Debounce rapid events untuk prevent spam
3. **Memory Management**: Clear old notifications dari memory
4. **Lazy Loading**: Connect socket hanya saat diperlukan

## Monitoring & Logging

Service menyediakan structured logging untuk monitoring:

```javascript
// Log levels: error, warn, info, debug
// Logs tersimpan di: notification-service/logs/notification-service.log

// Example log entries:
// [INFO] Socket connected: abc123 {"service":"notification-service","socketId":"abc123"}
// [INFO] Socket authenticated <NAME_EMAIL> {"socketId":"abc123","userId":"uuid"}
// [INFO] Notification sent to user uuid {"event":"analysis-complete","socketCount":2}
```

## Next Steps

1. Implementasikan salah satu contoh di atas sesuai framework yang digunakan
2. Tambahkan UI components untuk menampilkan notifications
3. Integrasikan dengan state management (Redux/Zustand/Vuex/NgRx)
4. Tambahkan sound notifications atau browser notifications
5. Implementasikan notification history/persistence
6. Setup monitoring dan alerting untuk production
7. Implementasikan graceful degradation jika WebSocket tidak tersedia
