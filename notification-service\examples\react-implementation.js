/**
 * React Hook untuk ATMA WebSocket Notifications
 * 
 * Usage:
 * import { useNotificationSocket } from './hooks/useNotificationSocket';
 * 
 * const { isConnected, isAuthenticated, notifications, clearNotifications } = 
 *   useNotificationSocket(authToken);
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { io } from 'socket.io-client';

// Configuration
const SOCKET_URL = process.env.REACT_APP_NOTIFICATION_SERVICE_URL || 'http://localhost:3005';
const RECONNECTION_ATTEMPTS = 5;
const RECONNECTION_DELAY = 1000;

export const useNotificationSocket = (token) => {
  const socketRef = useRef(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [connectionError, setConnectionError] = useState(null);

  // Clear notifications
  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Add notification
  const addNotification = useCallback((type, data) => {
    const notification = {
      id: Date.now() + Math.random(), // Simple ID generation
      type,
      timestamp: new Date().toISOString(),
      ...data
    };
    
    setNotifications(prev => [notification, ...prev]);
    
    // Auto-remove after 10 seconds for non-critical notifications
    if (type !== 'failed') {
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 10000);
    }
  }, []);

  // Remove specific notification
  const removeNotification = useCallback((id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  // Force reconnect
  const reconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current.connect();
    }
  }, []);

  useEffect(() => {
    if (!token) {
      setConnectionError('No authentication token provided');
      return;
    }

    // Initialize socket
    socketRef.current = io(SOCKET_URL, {
      autoConnect: false,
      transports: ['websocket', 'polling'],
      reconnection: true,
      reconnectionAttempts: RECONNECTION_ATTEMPTS,
      reconnectionDelay: RECONNECTION_DELAY,
      reconnectionDelayMax: 5000,
      timeout: 20000
    });

    const socket = socketRef.current;

    // Connection events
    socket.on('connect', () => {
      console.log('🔌 Connected to notification service');
      setIsConnected(true);
      setConnectionError(null);
      
      // Authenticate immediately after connection
      socket.emit('authenticate', { token });
    });

    socket.on('disconnect', (reason) => {
      console.log('🔌 Disconnected from notification service:', reason);
      setIsConnected(false);
      setIsAuthenticated(false);
      
      if (reason === 'io server disconnect') {
        // Server disconnected, manual reconnect needed
        setTimeout(() => socket.connect(), 1000);
      }
    });

    socket.on('reconnect', (attemptNumber) => {
      console.log('🔌 Reconnected after', attemptNumber, 'attempts');
      // Re-authenticate after reconnection
      socket.emit('authenticate', { token });
    });

    socket.on('reconnect_error', (error) => {
      console.error('🔌 Reconnection failed:', error);
      setConnectionError('Reconnection failed');
    });

    socket.on('reconnect_failed', () => {
      console.error('🔌 Reconnection failed after maximum attempts');
      setConnectionError('Failed to reconnect after multiple attempts');
    });

    // Authentication events
    socket.on('authenticated', (data) => {
      console.log('✅ Authentication successful:', data.email);
      setIsAuthenticated(true);
      setConnectionError(null);
    });

    socket.on('auth_error', (error) => {
      console.error('❌ Authentication failed:', error.message);
      setIsAuthenticated(false);
      setConnectionError(`Authentication failed: ${error.message}`);
      
      // Handle specific auth errors
      if (error.message === 'Authentication timeout') {
        // Retry authentication
        setTimeout(() => {
          socket.emit('authenticate', { token });
        }, 2000);
      } else if (error.message.includes('invalid') || error.message.includes('expired')) {
        // Token is invalid, need to refresh
        setConnectionError('Token expired or invalid. Please login again.');
      }
    });

    // Notification events
    socket.on('analysis-started', (data) => {
      console.log('📊 Analysis started:', data.jobId);
      addNotification('started', {
        title: 'Analysis Started',
        message: data.message || 'Your analysis has started processing...',
        jobId: data.jobId,
        metadata: data.metadata
      });
    });

    socket.on('analysis-complete', (data) => {
      console.log('✅ Analysis completed:', data.jobId);
      addNotification('completed', {
        title: 'Analysis Complete',
        message: data.message || 'Your analysis is ready!',
        jobId: data.jobId,
        resultId: data.resultId,
        metadata: data.metadata
      });
    });

    socket.on('analysis-failed', (data) => {
      console.log('❌ Analysis failed:', data.jobId);
      addNotification('failed', {
        title: 'Analysis Failed',
        message: data.message || 'Analysis failed. Please try again.',
        jobId: data.jobId,
        error: data.error,
        metadata: data.metadata
      });
    });

    // Generic error handling
    socket.on('error', (error) => {
      console.error('🔌 Socket error:', error);
      setConnectionError(`Socket error: ${error.message || error}`);
    });

    // Connect
    socket.connect();

    // Cleanup function
    return () => {
      console.log('🔌 Cleaning up socket connection');
      if (socket) {
        socket.removeAllListeners();
        socket.disconnect();
      }
    };
  }, [token, addNotification]);

  return {
    isConnected,
    isAuthenticated,
    notifications,
    connectionError,
    clearNotifications,
    removeNotification,
    reconnect
  };
};

/**
 * React Component untuk menampilkan notifications
 */
export const NotificationDisplay = ({ notifications, onRemove, onClear }) => {
  const getNotificationStyle = (type) => {
    const baseStyle = {
      padding: '12px 16px',
      margin: '8px 0',
      borderRadius: '8px',
      border: '1px solid',
      position: 'relative'
    };

    switch (type) {
      case 'started':
        return { ...baseStyle, backgroundColor: '#e3f2fd', borderColor: '#2196f3', color: '#1565c0' };
      case 'completed':
        return { ...baseStyle, backgroundColor: '#e8f5e8', borderColor: '#4caf50', color: '#2e7d32' };
      case 'failed':
        return { ...baseStyle, backgroundColor: '#ffebee', borderColor: '#f44336', color: '#c62828' };
      default:
        return { ...baseStyle, backgroundColor: '#f5f5f5', borderColor: '#ccc', color: '#333' };
    }
  };

  const getIcon = (type) => {
    switch (type) {
      case 'started': return '⏳';
      case 'completed': return '✅';
      case 'failed': return '❌';
      default: return 'ℹ️';
    }
  };

  if (notifications.length === 0) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>
        No notifications
      </div>
    );
  }

  return (
    <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <h3 style={{ margin: 0 }}>Notifications ({notifications.length})</h3>
        {notifications.length > 0 && (
          <button 
            onClick={onClear}
            style={{ 
              padding: '4px 8px', 
              fontSize: '12px', 
              backgroundColor: '#f0f0f0', 
              border: '1px solid #ccc',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Clear All
          </button>
        )}
      </div>
      
      {notifications.map((notification) => (
        <div key={notification.id} style={getNotificationStyle(notification.type)}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <div style={{ flex: 1 }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                <span style={{ marginRight: '8px', fontSize: '16px' }}>
                  {getIcon(notification.type)}
                </span>
                <strong>{notification.title}</strong>
              </div>
              <p style={{ margin: '4px 0', fontSize: '14px' }}>
                {notification.message}
              </p>
              {notification.jobId && (
                <small style={{ opacity: 0.7 }}>
                  Job ID: {notification.jobId}
                </small>
              )}
              {notification.metadata?.assessmentName && (
                <small style={{ opacity: 0.7, display: 'block' }}>
                  Assessment: {notification.metadata.assessmentName}
                </small>
              )}
              <small style={{ opacity: 0.7, display: 'block' }}>
                {new Date(notification.timestamp).toLocaleString()}
              </small>
            </div>
            <button
              onClick={() => onRemove(notification.id)}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '18px',
                cursor: 'pointer',
                opacity: 0.5,
                marginLeft: '8px'
              }}
              title="Remove notification"
            >
              ×
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * Connection Status Component
 */
export const ConnectionStatus = ({ isConnected, isAuthenticated, connectionError, onReconnect }) => {
  const getStatusColor = () => {
    if (connectionError) return '#f44336';
    if (isConnected && isAuthenticated) return '#4caf50';
    if (isConnected) return '#ff9800';
    return '#9e9e9e';
  };

  const getStatusText = () => {
    if (connectionError) return `Error: ${connectionError}`;
    if (isConnected && isAuthenticated) return 'Connected & Authenticated';
    if (isConnected) return 'Connected (Authenticating...)';
    return 'Disconnected';
  };

  return (
    <div style={{ 
      padding: '8px 12px', 
      backgroundColor: '#f5f5f5', 
      border: '1px solid #ddd',
      borderRadius: '4px',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      fontSize: '14px'
    }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div 
          style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: getStatusColor(),
            marginRight: '8px'
          }}
        />
        <span>{getStatusText()}</span>
      </div>
      
      {(connectionError || !isConnected) && (
        <button
          onClick={onReconnect}
          style={{
            padding: '4px 8px',
            fontSize: '12px',
            backgroundColor: '#2196f3',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Reconnect
        </button>
      )}
    </div>
  );
};
